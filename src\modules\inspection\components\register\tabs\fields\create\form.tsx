import { ICreateFields } from "@/modules/inspection/validators/fields/create";
import { Button } from "@/shared/components/shadcn/button";
import { FormField, FormItem, FormLabel, FormControl, FormMessage, Form } from "@/shared/components/shadcn/form";
import { Input } from "@/shared/components/shadcn/input";
import { UseFormReturn } from "react-hook-form";

interface IFormCreateFieldsProps {
	onClose: () => void;
	methods: UseFormReturn<ICreateFields>;
	onSubmit: (data: ICreateFields) => void;
}

export default function FormCreateFields({ onClose, methods, onSubmit }: IFormCreateFieldsProps) {
	return (
		<Form {...methods}>
			<form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-4">
				<FormField
					control={methods.control}
					name="name"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Nome</FormLabel>
							<FormControl>
								<Input {...field} placeholder="Digite o nome" />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<div className="flex justify-end gap-2 pt-4">
					<Button type="button" variant="outline" onClick={onClose}>
						Cancelar
					</Button>
					<Button type="submit">Salvar</Button>
				</div>
			</form>
		</Form>
	);
}
