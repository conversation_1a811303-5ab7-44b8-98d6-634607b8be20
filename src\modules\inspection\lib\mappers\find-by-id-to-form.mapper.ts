import { v4 as uuidv4 } from "uuid";
import { IFormFindByIdDto } from "../../types/forms/find-by-id.dto";
import { ICreateForm } from "../../validators/form/create";
import { ICreateFieldForm } from "../../validators/form/create-field";

export class InspectionFormFindByIdToFormMapper {
	static main({ title, text, nomenclature, developer, approver }: IFormFindByIdDto): ICreateForm {
		return {
			title,
			text,
			nomenclature,
			developer: {
				id: developer.id.toString(),
				name: developer.name,
			},
			approver: {
				id: approver.id.toString(),
				name: approver.name,
			},
		};
	}

	static fields({ fields }: IFormFindByIdDto): ICreateFieldForm[] {
		return fields.map(field => ({
			sequence: field.sequence,
			tempId: uuidv4(),
			field: {
				id: field.field?.id,
				name: field.field?.name,
			},
			groupTitle: field.groupTitle,
			nickname: field.nickname,
			required: field.required,
			group: field.group,
			typeId: field.fieldType?.id,
			options: field.options?.map(option => ({
				id: option.id,
				option: option.option,
				tempId: uuidv4(),
				sequence: option.sequence,
			})),
			measure: {
				id: field.measure?.id,
				name: field.measure?.name,
			},
			biFilter: field.biFilter,
		}));
	}
}
