import { IInspectionForm } from "@/modules/inspection/hooks/form/list/find-all.hook";
import { Badge } from "@/shared/components/shadcn/badge";
import { ColumnDef } from "@tanstack/react-table";
import { FormListActions } from "./actions";

export const columns: ColumnDef<IInspectionForm>[] = [
	{
		accessorKey: "titulo",
		header: () => <div className="text-start font-semibold">T<PERSON><PERSON><PERSON></div>,
		cell: ({ row }) => (
			<div className="text-start">
				<span className="font-medium text-primary truncate max-w-[200px] block ">{row.original.title}</span>
			</div>
		),
	},
	{
		accessorKey: "nomenclatura",
		header: () => <div className="text-center font-semibold">Nomenclatura</div>,
		cell: ({ row }) => (
			<div className="flex justify-center">
				<Badge variant="outline" className="bg-muted-foreground/10 text-xs px-3 py-1 rounded">
					{row.original.nomenclature}
				</Badge>
			</div>
		),
	},
	{
		accessorKey: "revisao",
		header: () => <div className="text-center font-semibold">Revisão</div>,
		cell: ({ row }) => (
			<div className="text-center">
				<span className="text-muted-foreground text-sm font-medium">{row.original.revision}</span>
			</div>
		),
	},
	{
		id: "actions",
		header: () => <div className="text-right font-semibold pr-2">Ações</div>,
		cell: ({ row }) => <FormListActions formId={row.original.id} title={row.original.title} />,
		enableSorting: false,
		enableHiding: false,
		size: 80,
	},
];
