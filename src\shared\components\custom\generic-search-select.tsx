import { Button } from "@/shared/components/shadcn/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/shared/components/shadcn/popover";
import { useDebounce } from "@/shared/hooks/utils/debounce.hook";
import { cn } from "@/shared/lib/shadcn/utils";
import { AlertCircleIcon, CheckIcon, ChevronsUpDownIcon, LoaderIcon, SearchIcon } from "lucide-react";
import { useState } from "react";

interface ISelectValue {
	id: number;
	name: string;
}

interface IDataItem {
	id: string | number;
	name: string;
}

interface IUseDataHookResult<T = IDataItem> {
	data: T[];
	isLoading: boolean;
	hasError: boolean;
	error: string | false | undefined;
}

interface IGenericSelectProps<T extends IDataItem = IDataItem> {
	value: ISelectValue;
	onChange: (value: ISelectValue) => void;
	useDataHook: (params: { limit: number; search: string; page: number }) => IUseDataHookResult<T>;
	placeholder?: string;
	searchPlaceholder?: string;
	loadingText?: string;
	emptyText?: string;
	width?: string;
}

export const GenericSearchSelect = <T extends IDataItem = IDataItem>({
	value,
	onChange,
	useDataHook,
	placeholder = "Selecione...",
	searchPlaceholder = "Buscar...",
	loadingText = "Carregando...",
	emptyText = "Nenhum item encontrado.",
	width = "w-[200px]",
}: IGenericSelectProps<T>) => {
	const [open, setOpen] = useState(false);
	const [searchTerm, setSearchTerm] = useState("");
	const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
	useDebounce((term: unknown) => setDebouncedSearchTerm(term as string), { delay: 300 })(searchTerm);

	const { data, isLoading, hasError, error } = useDataHook({
		limit: 50,
		search: debouncedSearchTerm,
		page: 1,
	});

	return (
		<Popover open={open} onOpenChange={setOpen}>
			<PopoverTrigger asChild>
				<Button variant="outline" role="combobox" aria-expanded={open} className={`${width} justify-between`} disabled={isLoading}>
					{value?.name || placeholder}
					<ChevronsUpDownIcon className="ml-2 h-4 w-4 opacity-50" />
				</Button>
			</PopoverTrigger>
			<PopoverContent className={`${width} p-0`}>
				<div className="flex flex-col">
					<div className="flex items-center border-b px-3 py-2">
						<SearchIcon className="mr-2 h-4 w-4 opacity-50" />
						<input
							type="text"
							placeholder={searchPlaceholder}
							value={searchTerm}
							onChange={e => setSearchTerm(e.target.value)}
							className="placeholder:text-muted-foreground w-full bg-transparent outline-none"
						/>
					</div>
					<div className="max-h-[200px] overflow-auto">
						{isLoading && (
							<div className="py-6 text-center text-sm">
								<LoaderIcon className="mx-auto mb-2 h-4 w-4 animate-spin" />
								<span className="text-muted-foreground">{loadingText}</span>
							</div>
						)}
						{hasError && (
							<div className="py-6 text-center text-sm">
								<AlertCircleIcon className="text-destructive mx-auto mb-2 h-4 w-4" />
								<span className="text-destructive">{error}</span>
							</div>
						)}
						{!isLoading &&
							!hasError &&
							(data.length > 0 ? (
								<div className="p-1">
									{data.map(item => (
										<div
											key={item.id}
											className={cn(
												"flex cursor-pointer items-center rounded-sm px-2 py-1.5 text-sm select-none",
												"hover:bg-accent hover:text-accent-foreground",
											)}
											onClick={() => {
												onChange({ id: Number(item.id), name: item.name });
												setOpen(false);
											}}
										>
											<CheckIcon className={cn("mr-2 h-4 w-4", value.id === Number(item.id) ? "opacity-100" : "opacity-0")} />
											{item.name}
										</div>
									))}
								</div>
							) : (
								<div className="text-muted-foreground py-6 text-center text-sm">{emptyText}</div>
							))}
					</div>
				</div>
			</PopoverContent>
		</Popover>
	);
};
