import { removeField<PERSON>ption<PERSON>tom, updateField<PERSON>ption<PERSON>tom } from "@/modules/inspection/atoms/forms/fields-form.atom";
import { Button } from "@/shared/components/shadcn/button";
import { Input } from "@/shared/components/shadcn/input";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { useSetAtom } from "jotai";
import { GripVertical, Trash2 } from "lucide-react";
import { forwardRef } from "react";

interface DraggableOptionProps {
	option: { sequence?: number; option: string; tempId: string; id?: number };
	fieldTempId: string;
	isLast: boolean;
}

export const DraggableOption = forwardRef<HTMLInputElement, DraggableOptionProps>(({ option, fieldTempId, isLast }, ref) => {
	const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
		id: option.tempId,
	});

	const removeFieldOption = useSet<PERSON>tom(removeFieldOption<PERSON>tom);
	const updateFieldOption = useSetAtom(updateFieldOptionAtom);

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
	};

	return (
		<div ref={setNodeRef} style={style} className={`flex items-center gap-2 ${isDragging ? "opacity-50" : ""}`}>
			<Button
				{...attributes}
				{...listeners}
				type="button"
				variant="ghost"
				size="icon"
				className="text-muted-foreground size-7 flex-shrink-0 cursor-grab hover:bg-transparent"
			>
				<GripVertical className="text-muted-foreground size-3" />
				<span className="sr-only">Arrastar para reordenar</span>
			</Button>
			<Input
				ref={isLast ? ref : undefined}
				className="flex-1"
				value={option.option}
				onChange={e =>
					updateFieldOption({
						tempId: fieldTempId,
						optionTempId: option.tempId,
						updatedOption: { ...option, option: e.target.value },
					})
				}
			/>
			<Button
				type="button"
				variant="ghost"
				size="sm"
				onClick={() => removeFieldOption({ tempId: fieldTempId, optionTempId: option.tempId })}
				className="text-destructive hover:text-destructive h-9 w-9 flex-shrink-0 p-0"
			>
				<Trash2 className="h-4 w-4" />
				<span className="sr-only">Remover opção</span>
			</Button>
		</div>
	);
});

DraggableOption.displayName = "DraggableOption";
