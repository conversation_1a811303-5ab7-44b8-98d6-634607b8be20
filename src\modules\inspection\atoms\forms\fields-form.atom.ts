import { atom } from "jotai";
import { InspectionFormTypeEnum } from "../../constants/form/type-enum";
import { ICreateFieldForm } from "../../validators/form/create-field";

export const fieldsFormAtom = atom<ICreateFieldForm[]>([]);

export const selectedFieldAtom = atom<string | null>(null);

export const fieldSelectAtom = atom(null, (get, set, tempId: string | null) => {
	set(selectedFieldAtom, tempId);
});

export const selectedFieldDataAtom = atom(get => {
	const selectedTempId = get(selectedFieldAtom);
	const fields = get(fieldsFormAtom);
	if (!selectedTempId) return null;
	return fields.find(field => field.tempId === selectedTempId) || null;
});

export const addFieldAtom = atom(null, (get, set, newField: ICreateFieldForm) => {
	const fields = get(fieldsFormAtom);
	set(fieldsForm<PERSON>tom, [...fields, newField]);
});

export const removeFieldAtom = atom(null, (get, set, fieldToRemove: ICreateFieldForm) => {
	const fields = get(fieldsForm<PERSON>tom);
	const filteredFields = fields.filter(field => field.tempId !== fieldToRemove.tempId);
	set(fieldsFormAtom, filteredFields);
});

export const updateFieldAtom = atom(null, (get, set, updatedField: ICreateFieldForm) => {
	const fields = get(fieldsFormAtom);
	const updatedFields = fields.map(field => (field.tempId === updatedField.tempId ? updatedField : field));
	set(fieldsFormAtom, updatedFields);
});

export const updateFieldSequenceAtom = atom(null, (get, set, { tempId, newSequence }: { tempId: string; newSequence: number }) => {
	const fields = get(fieldsFormAtom);
	const updatedFields = fields.map(field => (field.tempId === tempId ? { ...field, sequence: newSequence } : field));
	set(fieldsFormAtom, updatedFields);
});

export const updateFieldNicknameAtom = atom(null, (get, set, { tempId, nickname }: { tempId: string; nickname: string }) => {
	const fields = get(fieldsFormAtom);
	const updatedFields = fields.map(field => (field.tempId === tempId ? { ...field, nickname } : field));
	set(fieldsFormAtom, updatedFields);
});

export const updateFieldRequiredAtom = atom(null, (get, set, { tempId, required }: { tempId: string; required: boolean }) => {
	const fields = get(fieldsFormAtom);
	const updatedFields = fields.map(field => (field.tempId === tempId ? { ...field, required } : field));
	set(fieldsFormAtom, updatedFields);
});

export const updateFieldGroupAtom = atom(null, (get, set, { tempId, group }: { tempId: string; group: number | undefined }) => {
	const fields = get(fieldsFormAtom);
	const updatedFields = fields.map(field => (field.tempId === tempId ? { ...field, group } : field));
	set(fieldsFormAtom, updatedFields);
});

export const updateFieldGroupTitleAtom = atom(null, (get, set, { tempId, groupTitle }: { tempId: string; groupTitle: string }) => {
	const fields = get(fieldsFormAtom);
	const updatedFields = fields.map(field => (field.tempId === tempId ? { ...field, groupTitle } : field));
	set(fieldsFormAtom, updatedFields);
});

export const updateFieldTypeAtom = atom(null, (get, set, { tempId, typeId }: { tempId: string; typeId: InspectionFormTypeEnum }) => {
	const fields = get(fieldsFormAtom);
	const updatedFields = fields.map(field => (field.tempId === tempId ? { ...field, typeId } : field));
	set(fieldsFormAtom, updatedFields);
});

export const updateFieldMeasureAtom = atom(null, (get, set, { tempId, measure }: { tempId: string; measure: { id: number; name: string } }) => {
	const fields = get(fieldsFormAtom);
	const updatedFields = fields.map(field => (field.tempId === tempId ? { ...field, measure } : field));
	set(fieldsFormAtom, updatedFields);
});

export const updateFieldDataAtom = atom(null, (get, set, { tempId, field }: { tempId: string; field: { id?: number; name: string } }) => {
	const fields = get(fieldsFormAtom);
	const updatedFields = fields.map(fieldItem => (fieldItem.tempId === tempId ? { ...fieldItem, field } : fieldItem));
	set(fieldsFormAtom, updatedFields);
});

export const updateFieldBiFilterAtom = atom(null, (get, set, { tempId, biFilter }: { tempId: string; biFilter: boolean }) => {
	const fields = get(fieldsFormAtom);
	const updatedFields = fields.map(field => (field.tempId === tempId ? { ...field, biFilter } : field));
	set(fieldsFormAtom, updatedFields);
});

export const updateFieldOptionsAtom = atom(
	null,
	(get, set, { tempId, options }: { tempId: string; options: Array<{ sequence?: number; option: string; tempId: string; id?: number }> }) => {
		const fields = get(fieldsFormAtom);
		const updatedFields = fields.map(field => (field.tempId === tempId ? { ...field, options } : field));
		set(fieldsFormAtom, updatedFields);
	},
);

export const addFieldOptionAtom = atom(
	null,
	(get, set, { tempId, newOption }: { tempId: string; newOption: { sequence?: number; option: string; tempId: string; id?: number } }) => {
		const fields = get(fieldsFormAtom);
		const updatedFields = fields.map(field => {
			if (field.tempId === tempId) {
				const currentOptions = field.options || [];
				const nextSequence = currentOptions.length + 1;
				const optionWithSequence = { ...newOption, sequence: nextSequence };
				return { ...field, options: [...currentOptions, optionWithSequence] };
			}
			return field;
		});
		set(fieldsFormAtom, updatedFields);
	},
);

export const getFieldOptionsAtom = atom(get => {
	return (tempId: string) => {
		const fields = get(fieldsFormAtom);
		const field = fields.find(field => field.tempId === tempId);
		return field?.options || [];
	};
});

export const removeFieldOptionAtom = atom(null, (get, set, { tempId, optionTempId }: { tempId: string; optionTempId: string }) => {
	const fields = get(fieldsFormAtom);
	const updatedFields = fields.map(field => {
		if (field.tempId === tempId && field.options) {
			const filteredOptions = field.options.filter(option => option.tempId !== optionTempId);
			return { ...field, options: filteredOptions };
		}
		return field;
	});
	set(fieldsFormAtom, updatedFields);
});

export const updateFieldOptionAtom = atom(
	null,
	(
		get,
		set,
		{
			tempId,
			optionTempId,
			updatedOption,
		}: { tempId: string; optionTempId: string; updatedOption: { sequence?: number; option: string; tempId: string; id?: number } },
	) => {
		const fields = get(fieldsFormAtom);
		const updatedFields = fields.map(field => {
			if (field.tempId === tempId && field.options) {
				const updatedOptions = field.options.map(option => (option.tempId === optionTempId ? updatedOption : option));
				return { ...field, options: updatedOptions };
			}
			return field;
		});
		set(fieldsFormAtom, updatedFields);
	},
);

export const reorderFieldOptionsAtom = atom(
	null,
	(get, set, { tempId, activeIndex, overIndex }: { tempId: string; activeIndex: number; overIndex: number }) => {
		const fields = get(fieldsFormAtom);
		const updatedFields = fields.map(field => {
			if (field.tempId === tempId && field.options) {
				const newOptions = [...field.options];
				const [movedItem] = newOptions.splice(activeIndex, 1);
				newOptions.splice(overIndex, 0, movedItem);

				const optionsWithUpdatedSequence = newOptions.map((option, index) => ({
					...option,
					sequence: index + 1,
				}));

				return { ...field, options: optionsWithUpdatedSequence };
			}
			return field;
		});
		set(fieldsFormAtom, updatedFields);
	},
);

export const reorderFieldsAtom = atom(null, (get, set, { activeIndex, overIndex }: { activeIndex: number; overIndex: number }) => {
	const fields = get(fieldsFormAtom);
	const newFields = [...fields];
	const [movedItem] = newFields.splice(activeIndex, 1);
	newFields.splice(overIndex, 0, movedItem);

	const fieldsWithUpdatedSequence = newFields.map((field, index) => ({
		...field,
		sequence: index + 1,
	}));

	set(fieldsFormAtom, fieldsWithUpdatedSequence);
});

export const updateAllFieldSequencesAtom = atom(null, (get, set) => {
	const fields = get(fieldsFormAtom);
	const fieldsWithUpdatedSequence = fields.map((field, index) => ({
		...field,
		sequence: index + 1,
	}));
	set(fieldsFormAtom, fieldsWithUpdatedSequence);
});

