import { fieldS<PERSON>ct<PERSON><PERSON>, selectedField<PERSON>ata<PERSON><PERSON> } from "@/modules/inspection/atoms/forms/fields-form.atom";
import { InspectionFormTypeEnum } from "@/modules/inspection/constants/form/type-enum";
import { getGroupColor } from "@/modules/inspection/lib/utils/get-group-color";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { TableCell, TableRow } from "@/shared/components/shadcn/table";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { flexRender, Row } from "@tanstack/react-table";
import { useAtomValue, useSetAtom } from "jotai";
import { GripVertical } from "lucide-react";
import { InspectionFieldOptions } from "../options/field-options";

interface IGroupedRowProps {
	row: Row<ICreateFieldForm>;
	isGrouped?: boolean;
	indexInGroup: number;
}

const colorClasses = {
	blue: "border-blue-200 bg-blue-50 hover:bg-blue-100",
	green: "border-green-200 bg-green-50 hover:bg-green-100",
	gray: "border-gray-200 bg-gray-50 hover:bg-gray-100",
};

const getGroupClasses = (indexInGroup: number) => {
	const color = getGroupColor(indexInGroup) as keyof typeof colorClasses;
	return colorClasses[color];
};

const getBorderClass = (indexInGroup: number) => {
	const color = getGroupColor(indexInGroup);
	return `border-l-${color}-300`;
};

const getDotClass = (indexInGroup: number) => {
	const color = getGroupColor(indexInGroup);
	return `bg-${color}-400`;
};

const getLineClass = (indexInGroup: number) => {
	const color = getGroupColor(indexInGroup);
	return `bg-${color}-300`;
};

export const GroupedRow: React.FC<IGroupedRowProps> = ({ row, isGrouped = false, indexInGroup }) => {
	const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
		id: row.original.tempId,
	});
	const setSelectedRow = useSetAtom(fieldSelectAtom);
	const selected = useAtomValue(selectedFieldDataAtom);
	const showOptions = selected?.tempId === row.original.tempId && row.original.typeId === InspectionFormTypeEnum.OPTIONS;

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
		opacity: isDragging ? 0.5 : 1,
	};

	return (
		<>
			<TableRow
				ref={setNodeRef}
				style={style}
				onClick={() => setSelectedRow(row.original.tempId)}
				className={` ${isDragging ? "z-50" : ""} ${isGrouped ? `${getGroupClasses(indexInGroup)} border-l-4 ${getBorderClass(indexInGroup)}` : ""} ${!isGrouped ? "hover:bg-muted/50" : ""}`}
			>
				{row.getVisibleCells().map((cell, index) => {
					if (cell.column.id === "drag-handle") {
						return (
							<TableCell key={cell.id} className={`${isGrouped ? "pl-8" : "pl-2"} w-8`}>
								<div className="flex items-center gap-2">
									{isGrouped && (
										<div className="flex h-4 w-4 items-center justify-center">
											<div className={`h-2 w-2 rounded-full ${getDotClass(indexInGroup)}`} />
										</div>
									)}
									<button
										{...attributes}
										{...listeners}
										className="hover:bg-muted cursor-grab rounded p-1 active:cursor-grabbing"
										title="Arrastar para reordenar"
									>
										<GripVertical className="text-muted-foreground size-4" />
									</button>
								</div>
							</TableCell>
						);
					}

					return (
						<TableCell
							key={cell.id}
							className={` ${isGrouped && index === 1 ? "pl-8" : ""} ${cell.column.id === "field-name" && isGrouped ? "relative" : ""} `}
						>
							{cell.column.id === "field-name" && isGrouped && (
								<div className={`absolute top-1/2 left-2 h-px w-4 -translate-y-1/2 transform ${getLineClass(indexInGroup)}`} />
							)}
							{flexRender(cell.column.columnDef.cell, cell.getContext())}
						</TableCell>
					);
				})}
			</TableRow>
			{showOptions && (
				<TableRow>
					<TableCell colSpan={row.getVisibleCells().length}>
						<InspectionFieldOptions row={row} />
					</TableCell>
				</TableRow>
			)}
		</>
	);
};
