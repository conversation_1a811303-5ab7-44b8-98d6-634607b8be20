"use client";

import { ColumnDef } from "@tanstack/react-table";

export type IProdutoTableData = {
	solicitado: number;
	atendido: number;
	saldo: number;
	modelo: string;
	acabamento: string;
	largura: string;
	altura: string;
	situacao: string;
	ambiente: string;
	fechadura: string;
	dobradica: string;
	acessorios: string;
};

export const columns: ColumnDef<IProdutoTableData>[] = [
	{
		accessorKey: "solicitado",
		header: "Solicitado",
		id: "solicitado",
		size: 80,
		cell: ({ row }) => <div className="text-center font-medium">{row.getValue("solicitado")}</div>,
	},
	{
		accessorKey: "atendido",
		header: "Atendido",
		id: "atendido",
		size: 80,
		cell: ({ row }) => <div className="text-center font-medium text-green-600">{row.getValue("atendido")}</div>,
	},
	{
		accessorKey: "saldo",
		header: "Saldo",
		id: "saldo",
		size: 80,
		cell: ({ row }) => <div className="text-center font-medium text-red-600">{row.getValue("saldo")}</div>,
	},
	{
		accessorKey: "modelo",
		header: "Modelo",
		id: "modelo",
		size: 100,
		cell: ({ row }) => <div className="text-center">{row.getValue("modelo")}</div>,
	},
	{
		accessorKey: "acabamento",
		header: "Acabamento",
		id: "acabamento",
		size: 120,
		cell: ({ row }) => <div className="text-center">{row.getValue("acabamento")}</div>,
	},
	{
		accessorKey: "largura",
		header: "Largura",
		id: "largura",
		size: 100,
		cell: ({ row }) => {
			const largura = row.getValue("largura") as string;
			const isHighlight = largura.includes("205cm");
			return <div className={`text-center ${isHighlight ? "font-medium text-red-600" : ""}`}>{largura}</div>;
		},
	},
	{
		accessorKey: "altura",
		header: "Altura",
		id: "altura",
		size: 100,
		cell: ({ row }) => <div className="text-center">{row.getValue("altura")}</div>,
	},
	{
		accessorKey: "situacao",
		header: "Situação",
		id: "situacao",
		size: 100,
		cell: ({ row }) => <div className="text-center">{row.getValue("situacao")}</div>,
	},
	{
		accessorKey: "ambiente",
		header: "Ambiente",
		id: "ambiente",
		size: 100,
		cell: ({ row }) => <div className="text-center">{row.getValue("ambiente")}</div>,
	},
	{
		accessorKey: "fechadura",
		header: "Fechadura",
		id: "fechadura",
		size: 150,
		cell: ({ row }) => <div className="text-center font-medium text-red-600">{row.getValue("fechadura")}</div>,
	},
	{
		accessorKey: "dobradica",
		header: "Dobradiça",
		id: "dobradica",
		size: 150,
		cell: ({ row }) => <div className="text-center">{row.getValue("dobradica")}</div>,
	},
	{
		accessorKey: "acessorios",
		header: "Acessórios",
		id: "acessorios",
		size: 120,
		cell: ({ row }) => <div className="text-center">{row.getValue("acessorios")}</div>,
	},
];
