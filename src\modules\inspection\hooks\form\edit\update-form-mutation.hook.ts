import { toast } from "@/core/toast";
import { INSPECTION_FORM_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { IUpdateFormDTO } from "@/modules/inspection/types/forms/update-form.dto";
import { createPutRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { useMutation } from "@tanstack/react-query";

export const useUpdateFormMutation = () => {
	const updateFormMutation = useMutation({
		mutationKey: ["update-inspection-form"],
		mutationFn: async ({ form, id }: { form: IUpdateFormDTO; id: string }) => {
			const res = await createPutRequest<ApiResponse<IMessageGlobalReturn>>(INSPECTION_FORM_ENDPOINTS.UPDATE(id), form);
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
	});

	return {
		updateForm: (variables: { form: IUpdateFormDTO; id: string }) =>
			toast.promise(updateFormMutation.mutateAsync(variables), {
				loading: "Atualizando formulário...",
				success: "Formulário atualizado com sucesso!",
				error: "Erro ao atualizar formulário.",
			}),
	};
};
