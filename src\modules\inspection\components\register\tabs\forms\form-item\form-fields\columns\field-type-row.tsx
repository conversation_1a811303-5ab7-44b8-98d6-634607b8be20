import { updateField<PERSON><PERSON><PERSON><PERSON> } from "@/modules/inspection/atoms/forms/fields-form.atom";
import { InspectionFormTypeEnum } from "@/modules/inspection/constants/form/type-enum";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/shadcn/select";
import { Row } from "@tanstack/react-table";
import { useSetAtom } from "jotai";

export const InspectionFormFieldTypeRow = ({ row }: { row: Row<ICreateFieldForm> }) => {
	const { tempId, typeId } = row.original;
	const updateField = useSetAtom(updateFieldTypeAtom);

	return (
		<Select
			value={typeId?.toString()}
			onValueChange={value => {
				const typeId = Number(value) as InspectionFormTypeEnum;
				updateField({ tempId, typeId });
			}}
		>
			<SelectTrigger className="w-38 **:data-[slot=select-value]:block **:data-[slot=select-value]:truncate">
				<SelectValue placeholder="Tipo de campo" />
			</SelectTrigger>
			<SelectContent align="end">
				<SelectItem value={InspectionFormTypeEnum.DATE.toString()}>Data</SelectItem>
				<SelectItem value={InspectionFormTypeEnum.TEXT.toString()}>Texto</SelectItem>
				<SelectItem value={InspectionFormTypeEnum.OPTIONS.toString()}>Opções</SelectItem>
				<SelectItem value={InspectionFormTypeEnum.NUMBER.toString()}>Número</SelectItem>
				<SelectItem value={InspectionFormTypeEnum.BOOLEAN.toString()}>Booleano</SelectItem>
			</SelectContent>
		</Select>
	);
};
