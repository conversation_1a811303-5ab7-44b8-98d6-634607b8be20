import { addField<PERSON>tom, fields<PERSON><PERSON><PERSON><PERSON> } from "@/modules/inspection/atoms/forms/fields-form.atom";
import { createDefaultField } from "@/modules/inspection/constants/form/default-field-value";
import { useInspectionFormTableField } from "@/modules/inspection/hooks/form/create/table-field.hook";
import { Button } from "@/shared/components/shadcn/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/shadcn/table";
import { closestCenter, DndContext } from "@dnd-kit/core";
import { restrictToParentElement, restrictToVerticalAxis } from "@dnd-kit/modifiers";
import { SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { flexRender } from "@tanstack/react-table";
import { useAtomValue, useSetAtom } from "jotai";
import { Plus } from "lucide-react";
import { inspectionFormColumns } from "./columns/columns";
import { DraggableRow } from "./columns/drag-reorder-row";

export const NewTable: React.FC = () => {
	const fields = useAtomValue(fieldsFormAtom);
	const addField = useSetAtom(addFieldAtom);
	const { table, sensors, sortableId, dataIds, handleDragEnd, handleDragStart } = useInspectionFormTableField();

	return (
		<div className="mt-2 flex flex-col gap-4">
			<div className="hidden w-full justify-end lg:flex">
				<Button
					variant="outline"
					onClick={e => {
						e.preventDefault();
						addField(createDefaultField(fields));
					}}
				>
					<Plus className="ml-2 size-4" />
					Adicionar campo
				</Button>
			</div>

			<div className="hidden max-h-[500px] w-full overflow-auto rounded-lg border lg:block">
				<DndContext
					collisionDetection={closestCenter}
					modifiers={[restrictToVerticalAxis, restrictToParentElement]}
					onDragEnd={handleDragEnd}
					onDragStart={handleDragStart}
					sensors={sensors}
					id={sortableId}
				>
					<Table>
						<TableHeader className="bg-muted sticky top-0 z-10">
							{table.getHeaderGroups().map(headerGroup => (
								<TableRow key={headerGroup.id}>
									{headerGroup.headers.map(header => {
										return (
											<TableHead key={header.id} colSpan={header.colSpan}>
												{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
											</TableHead>
										);
									})}
								</TableRow>
							))}
						</TableHeader>
						<TableBody className="**:data-[slot=table-cell]:first:w-8">
							{table.getRowModel().rows?.length ? (
								<SortableContext items={dataIds} strategy={verticalListSortingStrategy}>
									{table.getRowModel().rows.map(row => (
										<DraggableRow key={row.id} row={row} />
									))}
								</SortableContext>
							) : (
								<TableRow>
									<TableCell colSpan={inspectionFormColumns.length} className="h-24 text-center">
										Nenhum resultado.
									</TableCell>
								</TableRow>
							)}
						</TableBody>
					</Table>
				</DndContext>
			</div>
		</div>
	);
};
