import { toast } from "@/core/toast";
import { MEASURES_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { ICreateMeasuresDTO } from "@/modules/inspection/types/measures/create-measures.dto";
import { createPostRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export function useCreateMeasuresMutation() {
	const queryClient = useQueryClient();

	const createMeasuresMutations = useMutation({
		mutationKey: ["create-measures"],
		mutationFn: async (form: ICreateMeasuresDTO) => {
			const res = await createPostRequest<ApiResponse<IMessageGlobalReturn>>(MEASURES_ENDPOINTS.CREATE, form);
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["measures"],
				exact: false,
			});
		},
	});

	return {
		createMeasures: (form: ICreateMeasuresDTO) =>
			toast.promise(createMeasuresMutations.mutateAsync(form), {
				loading: "Criando medida...",
				success: "Medida criada com sucesso.",
				error: "Erro ao criar medida.",
			}),
	};
}
