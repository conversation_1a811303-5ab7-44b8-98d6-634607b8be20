import { toast } from "@/core/toast";
import { FIELDS_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { ICreateFieldsDTO } from "@/modules/inspection/types/fields/create-fields.dto";
import { createPostRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export function useCreateFieldsMutation() {
	const queryClient = useQueryClient();

	const createFieldsMutations = useMutation({
		mutationKey: ["create-fields"],
		mutationFn: async (form: ICreateFieldsDTO) => {
			const res = await createPostRequest<ApiResponse<IMessageGlobalReturn>>(FIELDS_ENDPOINTS.CREATE, form);
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["fields"],
				exact: false,
			});
		},
	});
	return {
		createFields: (form: ICreateFieldsDTO) =>
			toast.promise(createFieldsMutations.mutateAsync(form), {
				loading: "Criando campo...",
				success: "Campo criado com sucesso.",
				error: "Erro ao criar campo.",
			}),
	};
}
