import { fields<PERSON><PERSON><PERSON><PERSON> } from "@/modules/inspection/atoms/forms/fields-form.atom";
import { useCreateForm } from "@/modules/inspection/hooks/form/create/create-form.hook";
import { useUpdateFormMutation } from "@/modules/inspection/hooks/form/edit/update-form-mutation.hook";
import { useFormFindById } from "@/modules/inspection/hooks/form/list/find-by-id.hook";
import { InspectionFormFindByIdToFormMapper } from "@/modules/inspection/lib/mappers/find-by-id-to-form.mapper";
import { InspectionFormToUpdateMapper } from "@/modules/inspection/lib/mappers/form-to-update.mapper";
import { ICreateForm } from "@/modules/inspection/validators/form/create";
import { Modal } from "@/shared/components/custom/modal";
import { useSetAtom } from "jotai";
import { useEffect } from "react";
import { FormCreateForm } from "../form-item/form";

interface IModalEditFormProps {
	isOpen: boolean;
	onClose: () => void;
	formId: string;
}

export const ModalEditForm: React.FC<IModalEditFormProps> = ({ isOpen, onClose, formId }) => {
	const { methods, ...formActions } = useCreateForm();
	const { updateForm } = useUpdateFormMutation();
	const { data, isLoading, hasError, error } = useFormFindById(formId, isOpen);
	const setFields = useSetAtom(fieldsFormAtom);

	useEffect(() => {
		if (data) {
			const mappedDataMain = InspectionFormFindByIdToFormMapper.main(data);
			const mappedDataFields = InspectionFormFindByIdToFormMapper.fields(data);
			methods.reset(mappedDataMain);
			setFields(mappedDataFields);
		}
	}, [data, methods, setFields]);

	const handleSubmit = (formData: ICreateForm) => {
		const updateData = InspectionFormToUpdateMapper.map(formData);
		updateForm({ form: updateData, id: formId });
		onClose();
	};

	return (
		<Modal
			isOpen={isOpen}
			onClose={onClose}
			className="!h-[95vh] !w-full !max-w-[95vw] md:!h-auto md:!w-[1400px] md:!max-w-none"
			title="Edição de Formulário"
		>
			{isLoading && <div>Carregando...</div>}
			{hasError && <div className="text-red-500">{error}</div>}
			{data && <FormCreateForm mode="edit" onClose={onClose} methods={methods} onSubmit={handleSubmit} {...formActions} />}
		</Modal>
	);
};
