import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { ColumnDef } from "@tanstack/react-table";
import { InspectionFormActionsRow } from "./actions-row";
import { InspectionFormBiFilterRow } from "./bi-filter-row";
import { DragReorderRow } from "./drag-reorder-row";
import { InspectionFormFieldRow } from "./field-row";
import { InspectionFormFieldTypeRow } from "./field-type-row";
import { InspectionFormMeasureRow } from "./measure-row";
import { InspectionFormNicknameRow } from "./nickname-row";
import { InspectionFormRequiredRow } from "./required-row";

export const inspectionFormColumns: ColumnDef<ICreateFieldForm>[] = [
	{
		id: "drag-handle",
		header: () => null,
		cell: ({ row }) => <DragReorderRow id={row.original.tempId} />,
	},
	{
		id: "field-name",
		header: "Campo",
		cell: ({ row }) => <InspectionFormFieldRow row={row} />,
	},
	{
		id: "nickname",
		header: "Apelido",
		cell: ({ row }) => <InspectionFormNicknameRow row={row} />,
	},
	{
		id: "field-type",
		header: "Tipo",
		cell: ({ row }) => <InspectionFormFieldTypeRow row={row} />,
	},
	{
		id: "measure",
		header: "Medida",
		cell: ({ row }) => <InspectionFormMeasureRow row={row} />,
	},
	{
		id: "bi-filter",
		header: "Filtro BI",
		cell: ({ row }) => <InspectionFormBiFilterRow row={row} />,
	},
	{
		id: "required",
		header: "Obrigatório",
		cell: ({ row }) => <InspectionFormRequiredRow row={row} />,
	},

	{
		id: "actions",
		header: "Ações",
		cell: ({ row }) => <InspectionFormActionsRow row={row} />,
	},
];
