import { createFieldsSchema, ICreateFields } from "@/modules/inspection/validators/fields/create";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, UseFormReturn } from "react-hook-form";

interface UseCreateFieldsReturn {
	methods: UseFormReturn<ICreateFields>;
}

export function useCreateFields(): UseCreateFieldsReturn {
	const methods = useForm<ICreateFields>({
		resolver: zodResolver(createFieldsSchema),
		defaultValues: {
			name: "",
		},
		mode: "onChange",
	});
	return { methods };
}
