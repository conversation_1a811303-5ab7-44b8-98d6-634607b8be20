import React from "react";

import { IToastService, ToastConfiguration, ToastOptions, ToastType } from "../types/toast.type";
import { ToastPresenter } from "./toast.presenter";
import { JotaiToastStore } from "./toast.store";

class ToastService implements IToastService {
	private readonly store: JotaiToastStore;
	private readonly presenter: ToastPresenter;

	constructor() {
		this.store = new JotaiToastStore();
		this.presenter = new ToastPresenter(this.store);
	}

	success(message: string, options?: ToastOptions): string {
		return this.presenter.success(message, options);
	}

	error(message: string | string[], options?: ToastOptions): string {
		return this.presenter.error(message, options);
	}

	warning(message: string, options?: ToastOptions): string {
		return this.presenter.warning(message, options);
	}

	info(message: string, options?: ToastOptions): string {
		return this.presenter.info(message, options);
	}

	loading(message: string, options?: ToastOptions): string {
		return this.presenter.loading(message, options);
	}

	custom(customContent: React.ReactNode, type: ToastType = "info", options?: ToastOptions): string {
		return this.presenter.custom(customContent, type, options);
	}

	dismiss(id: string): void {
		this.store.removeToast(id);
	}

	pauseTimer(id: string): void {
		this.store.pauseToastTimer(id);
	}

	resumeTimer(id: string, remainingTime: number): void {
		this.store.resumeToastTimer(id, remainingTime);
	}

	async promise<T>(
		promise: Promise<T>,
		messages: {
			loading: string;
			success?: string;
			error?: string;
		},
		options?: ToastOptions
	): Promise<T> {
		const toastId = this.loading(messages.loading, options);

		try {
			const result = await promise;
			this.dismiss(toastId);

			if (messages.success) {
				this.success(messages.success, options);
			}

			return result;
		} catch (error) {
			this.dismiss(toastId);
			const errorMessage = messages.error || this.extractErrorMessage(error);
			this.error(errorMessage, options);

			throw error;
		}
	}

	private extractErrorMessage(error: unknown): string {
		if (error instanceof Error) {
			return error.message;
		}

		if (typeof error === "string") {
			return error;
		}

		if (error && typeof error === "object") {
			const errorObj = error as Record<string, unknown>;
			if (typeof errorObj.message === "string") return errorObj.message;
			if (typeof errorObj.error === "string") return errorObj.error;
			if (errorObj.data && typeof errorObj.data === "object" && errorObj.data !== null) {
				const dataObj = errorObj.data as Record<string, unknown>;
				if (typeof dataObj.message === "string") {
					return dataObj.message;
				}
			}

			if (errorObj.response && typeof errorObj.response === "object" && errorObj.response !== null) {
				const responseObj = errorObj.response as Record<string, unknown>;
				if (responseObj.data && typeof responseObj.data === "object" && responseObj.data !== null) {
					const responseData = responseObj.data as Record<string, unknown>;
					if (typeof responseData.message === "string") {
						return responseData.message;
					}
					if (typeof responseData.error === "string") {
						return responseData.error;
					}
				}
			}
		}

		return "Erro inesperado";
	}

	configure(config: Partial<ToastConfiguration>): void {
		this.store.updateToastConfig(config);
	}

	// 	update(id: string, message: string, options?: Partial<ToastOptions>): void {
	// 		this.store.updateToast(id, message, options);
	// 	}

	// 	clearAll(types?: ToastType[]): void {
	// 		this.store.clearAllToasts(types);
	// 	}
	// }
}
// Export a singleton instance
export const toast = new ToastService();
