import { fields<PERSON><PERSON><PERSON><PERSON> } from "@/modules/inspection/atoms/forms/fields-form.atom";
import { addFieldToGroupAtom, removeGroup<PERSON><PERSON>, updateGroupTitleAtom } from "@/modules/inspection/atoms/forms/group-fields.atom";
import { createDefaultField } from "@/modules/inspection/constants/form/default-field-value";
import { getGroupColor } from "@/modules/inspection/lib/utils/get-group-color";
import { Button } from "@/shared/components/shadcn/button";
import { Input } from "@/shared/components/shadcn/input";
import { TableCell, TableRow } from "@/shared/components/shadcn/table";
import { useAtomValue, useSetAtom } from "jotai";
import { Edit, Plus, Trash2, Users } from "lucide-react";
import { useState } from "react";

interface IGroupHeaderProps {
	group: { id: number; title: string; tempId: string };
	colSpan: number;
	indexInGroup: number;
}

export const GroupHeader: React.FC<IGroupHeaderProps> = ({ group, colSpan, indexInGroup }) => {
	const [isEditing, setIsEditing] = useState(false);
	const [editTitle, setEditTitle] = useState(group.title);
	const fields = useAtomValue(fieldsFormAtom);
	const updateGroupTitle = useSetAtom(updateGroupTitleAtom);
	const removeGroup = useSetAtom(removeGroupAtom);
	const addFieldToGroup = useSetAtom(addFieldToGroupAtom);

	const handleSaveTitle = () => {
		updateGroupTitle({ tempId: group.tempId, title: editTitle });
		setIsEditing(false);
	};

	const handleCancelEdit = () => {
		setEditTitle(group.title);
		setIsEditing(false);
	};

	const handleRemoveGroup = () => removeGroup(group.tempId);

	const handleAddFieldToGroup = () => {
		const newField = createDefaultField(fields);
		addFieldToGroup({ groupId: group.id, newField });
	};

	return (
		<TableRow
			className={`border-b-2 border-${getGroupColor(indexInGroup)}-200 bg-${getGroupColor(indexInGroup)}-50 hover:bg-${getGroupColor(indexInGroup)}-100`}
		>
			<TableCell colSpan={colSpan} className="py-3">
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-3">
						<Users className={`size-5 text-${getGroupColor(indexInGroup)}-600`} />
						{isEditing ? (
							<div className="flex items-center gap-2">
								<Input
									value={editTitle}
									onChange={e => setEditTitle(e.target.value)}
									className="h-8 w-64"
									placeholder="Título do grupo"
									onKeyDown={e => {
										if (e.key === "Enter") {
											handleSaveTitle();
										} else if (e.key === "Escape") {
											handleCancelEdit();
										}
									}}
									autoFocus
								/>
								<Button size="sm" onClick={handleSaveTitle} className="h-8 px-3">
									Salvar
								</Button>
								<Button size="sm" variant="outline" onClick={handleCancelEdit} className="h-8 px-3">
									Cancelar
								</Button>
							</div>
						) : (
							<div className="flex items-center gap-2">
								<span
									className={`cursor-pointer font-semibold text-${getGroupColor(indexInGroup)}-800 hover:${getGroupColor(indexInGroup)}-blue-900`}
									onClick={() => setIsEditing(true)}
									title="Clique para editar o título do grupo"
								>
									{group.title || "Grupo sem título"}
								</span>
								<Button
									size="sm"
									variant="ghost"
									onClick={() => setIsEditing(true)}
									className={`h-6 w-6 p-0 text-${getGroupColor(indexInGroup)}-600 hover:text-${getGroupColor(indexInGroup)}-800`}
									title="Editar título do grupo"
								>
									<Edit className="size-4" />
								</Button>
							</div>
						)}
					</div>
					<div className="flex items-center gap-2">
						<Button
							size="sm"
							variant="outline"
							onClick={handleAddFieldToGroup}
							className={`h-8 border-${getGroupColor(indexInGroup)}-300 px-3 text-${getGroupColor(indexInGroup)}-600 hover:bg-${getGroupColor(indexInGroup)}-50`}
							title="Adicionar campo ao grupo"
						>
							<Plus className="mr-1 size-4" />
							Adicionar Campo
						</Button>
						<Button
							size="sm"
							variant="ghost"
							onClick={handleRemoveGroup}
							className="h-8 w-8 p-0 text-red-600 hover:bg-red-100 hover:text-red-800"
							title="Remover grupo"
						>
							<Trash2 className="size-4" />
						</Button>
					</div>
				</div>
			</TableCell>
		</TableRow>
	);
};
