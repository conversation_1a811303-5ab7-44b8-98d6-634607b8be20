import { addField<PERSON>tom, fieldsForm<PERSON><PERSON> } from "@/modules/inspection/atoms/forms/fields-form.atom";
import {
	addFieldToGroupAtom,
	addGroup<PERSON>tom,
	getFieldsByGroupAtom,
	getNextGroupIdAtom,
	updateFieldSequencesInGroupAtom,
} from "@/modules/inspection/atoms/forms/group-fields.atom";
import { createDefaultField } from "@/modules/inspection/constants/form/default-field-value";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { useAtomValue, useSetAtom } from "jotai";
import { useCallback } from "react";
import { v4 as uuidv4 } from "uuid";

interface IUseGroupManagementReturn {
	groups: Array<{ id: number; title: string; tempId: string }>;
	groupedFields: Map<number | undefined, ICreateFieldForm[]>;
	addGroup: () => void;
	addFieldToGroup: (groupId: number) => void;
	addStandaloneField: () => void;
	updateSequencesInGroup: (groupId: number | undefined) => void;
}


// cores de grupos 
//  

export const useGroupManagement = (): IUseGroupManagementReturn => {
	const fields = useAtomValue(fieldsFormAtom);
	const { groupedFields, groups } = useAtomValue(getFieldsByGroupAtom);
	const nextGroupId = useAtomValue(getNextGroupIdAtom);

	const addGroupAction = useSetAtom(addGroupAtom);
	const addFieldAction = useSetAtom(addFieldAtom);
	const addFieldToGroupAction = useSetAtom(addFieldToGroupAtom);
	const updateSequencesAction = useSetAtom(updateFieldSequencesInGroupAtom);

	const addGroup = useCallback(() => {
		const newGroup = {
			id: nextGroupId,
			title: `Grupo ${nextGroupId}`,
			tempId: uuidv4(),
		};
		addGroupAction(newGroup);
	}, [nextGroupId, addGroupAction]);

	const addFieldToGroup = useCallback(
		(groupId: number) => {
			const newField = createDefaultField(fields);
			addFieldToGroupAction({ groupId, newField });
		},
		[fields, addFieldToGroupAction],
	);

	const addStandaloneField = useCallback(() => {
		const newField = createDefaultField(fields);
		addFieldAction(newField);
	}, [fields, addFieldAction]);

	const updateSequencesInGroup = useCallback(
		(groupId: number | undefined) => {
			updateSequencesAction(groupId);
		},
		[updateSequencesAction],
	);

	return {
		groups,
		groupedFields,
		addGroup,
		addFieldToGroup,
		addStandaloneField,
		updateSequencesInGroup,
	};
};
