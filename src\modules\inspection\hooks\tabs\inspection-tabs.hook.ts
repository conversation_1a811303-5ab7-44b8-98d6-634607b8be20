"use client";

import { useAtom } from "jotai";
import { useCallback, useMemo } from "react";
import { activeTabsAtom } from "../../atoms/active-tabs.atom";

export interface IUseInspectionTabsReturn {
	activeTab: TInspectionTabValue;
	setActiveTab: (tab: TInspectionTabValue) => void;
	isTabActive: (tab: TInspectionTabValue) => boolean;
	goToNextTab: () => void;
	goToPreviousTab: () => void;
	availableTabs: readonly TInspectionTabValue[];
}

export type TInspectionTabValue = "medidas" | "campos" | "vinculo-colaboradores" | "formularios" | "celulas" | "componentes" | "vinculos";

export const useInspectionTabs = (): IUseInspectionTabsReturn => {
	const [activeTab, setActiveTab] = useAtom(activeTabsAtom);

	const availableTabs: readonly TInspectionTabValue[] = useMemo(
		() => ["medidas", "campos", "vinculo-colaboradores", "formularios", "celulas", "componentes", "vinculos"] as const,
		[],
	);

	const isTabActive = useCallback(
		(tab: TInspectionTabValue): boolean => {
			return activeTab === tab;
		},
		[activeTab],
	);

	const goToNextTab = useCallback(() => {
		const currentIndex = availableTabs.indexOf(activeTab);
		const nextIndex = (currentIndex + 1) % availableTabs.length;
		setActiveTab(availableTabs[nextIndex]);
	}, [activeTab, setActiveTab, availableTabs]);

	const goToPreviousTab = useCallback(() => {
		const currentIndex = availableTabs.indexOf(activeTab);
		const previousIndex = currentIndex === 0 ? availableTabs.length - 1 : currentIndex - 1;
		setActiveTab(availableTabs[previousIndex]);
	}, [activeTab, setActiveTab, availableTabs]);

	return {
		activeTab,
		setActiveTab,
		isTabActive,
		goToNextTab,
		goToPreviousTab,
		availableTabs,
	};
};
