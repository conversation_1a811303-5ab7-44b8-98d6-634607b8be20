import { FIELDS_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { createGetRequest } from "@/shared/lib/requests";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";
import { useQuery } from "@tanstack/react-query";

export interface IFields {
	id: string;
	name: string;
}

export interface IFindAllFieldsParamns {
	page?: number;
	limit?: number;
	search?: string;
}

export default function useFindAllFields({ page = 1, limit = 10, search = "" }: IFindAllFieldsParamns) {
	const queryKey = ["fields", { page, limit, search }];
	const { data, isLoading, isFetched } = useQuery<ApiResponse<IResponsePaginated<IFields>>>({
		queryKey,
		queryFn: () => createGetRequest<IResponsePaginated<IFields>>(FIELDS_ENDPOINTS.FIND_ALL({ page, limit, search })),
	});
	return {
		data: data?.success ? data.data.data : [],
		pagination: data?.success
			? {
					totalItems: data.data.totalItems,
					itemsPerPage: data.data.itemsPerPage,
					currentPage: data.data.currentPage,
					totalPages: data.data.totalPages,
				}
			: null,
		isLoading,
		hasError: isFetched && !data?.success,
		error: !data?.success && data?.data.message,
	};
}
