import { updateFieldBi<PERSON>ilter<PERSON><PERSON> } from "@/modules/inspection/atoms/forms/fields-form.atom";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { Checkbox } from "@/shared/components/shadcn/checkbox";
import { Row } from "@tanstack/react-table";
import { useSetAtom } from "jotai";

export const InspectionFormBiFilterRow = ({ row }: { row: Row<ICreateFieldForm> }) => {
	const { tempId, biFilter } = row.original;
	const updateField = useSetAtom(updateFieldBiFilterAtom);

	return <Checkbox checked={biFilter === true} onCheckedChange={checked => updateField({ tempId, biFilter: !!checked })} />;
};
