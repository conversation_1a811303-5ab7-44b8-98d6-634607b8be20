"use client";
import { INSPECTION_FORM_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { createGetRequest } from "@/shared/lib/requests";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";
import { useQuery } from "@tanstack/react-query";

export interface IInspectionForm {
	id: string;
	title: string;
	nomenclature: string;
	revision: number;
}

export interface IFindAllInspectionFormParams {
	page?: number;
	limit?: number;
	search?: string;
}

export const useFindAllInspectionForm = ({ page = 1, limit = 10, search = "" }: IFindAllInspectionFormParams = {}) => {
	const { data, isLoading, isFetched } = useQuery({
		queryKey: ["inspection-form", { page, limit, search }],
		queryFn: () => createGetRequest<IResponsePaginated<IInspectionForm>>(INSPECTION_FORM_ENDPOINTS.FIND_ALL({ page, limit, search })),
	});

	return {
		data: data?.success ? data.data.data : [],
		pagination: data?.success
			? {
					totalItems: data.data.totalItems,
					itemsPerPage: data.data.itemsPerPage,
					currentPage: data.data.currentPage,
					totalPages: data.data.totalPages,
				}
			: null,
		isLoading,
		hasError: isFetched && !data?.success,
		error: !data?.success && data?.data.message,
	};
};
