import { fieldSelect<PERSON><PERSON>, fields<PERSON>orm<PERSON><PERSON>, reorderFieldsAtom } from "@/modules/inspection/atoms/forms/fields-form.atom";
import { inspectionFormColumns } from "@/modules/inspection/components/register/tabs/forms/form-item/form-fields/columns/columns";
import { DragEndEvent, KeyboardSensor, MouseSensor, TouchSensor, useSensor, useSensors } from "@dnd-kit/core";
import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { useAtomValue, useSetAtom } from "jotai";
import { useId, useMemo } from "react";

export const useInspectionFormTableField = () => {
	const data = useAtomValue(fieldsFormAtom);
	const setSelectedField = useSetAtom(fieldSelectAtom);
	const reorderFields = useSetAtom(reorderFieldsAtom);
	const sortableId = useId();
	const sensors = useSensors(useSensor(MouseSensor, {}), useSensor(TouchSensor, {}), useSensor(KeyboardSensor, {}));
	const dataIds = useMemo<string[]>(() => data.map(item => item.tempId), [data]);

	const table = useReactTable({
		data,
		columns: inspectionFormColumns,
		getRowId: row => row.tempId,
		getCoreRowModel: getCoreRowModel(),
	});

	const handleDragEnd = (event: DragEndEvent) => {
		const { active, over } = event;
		if (active && over && active.id !== over.id) {
			const activeIndex = dataIds.indexOf(String(active.id));
			const overIndex = dataIds.indexOf(String(over.id));
			if (activeIndex !== -1 && overIndex !== -1) {
				reorderFields({ activeIndex, overIndex });
				setSelectedField(String(active.id));
			}
		}
	};

	const handleDragStart = () => setSelectedField(null);

	return {
		data,
		table,
		sensors,
		sortableId,
		dataIds,
		handleDragEnd,
		handleDragStart,
	};
};
