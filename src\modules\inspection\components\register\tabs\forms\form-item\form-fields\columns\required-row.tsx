import { updateFieldRequired<PERSON>tom } from "@/modules/inspection/atoms/forms/fields-form.atom";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { Checkbox } from "@/shared/components/shadcn/checkbox";

import { Row } from "@tanstack/react-table";
import { useSet<PERSON>tom } from "jotai";

export const InspectionFormRequiredRow = ({ row }: { row: Row<ICreateFieldForm> }) => {
	const { tempId, required } = row.original;
	const updateField = useSetAtom(updateFieldRequiredAtom);

	return <Checkbox checked={required === true} onCheckedChange={checked => updateField({ tempId, required: !!checked })} />;
};
