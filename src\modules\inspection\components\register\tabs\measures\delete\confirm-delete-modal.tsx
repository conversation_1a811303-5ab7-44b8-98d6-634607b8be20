import { useDeleteMeasures } from "@/modules/inspection/hooks/measures/delete/delete-measures-mutation.hook";
import { Modal } from "@/shared/components/custom/modal";
import { Button } from "@/shared/components/shadcn/button";
import { Trash } from "lucide-react";

export interface IConfirmDeleteMeasuresModalProps {
	measuresId: string;
	isOpen: boolean;
	name?: string;
	onClose: () => void;
}

export function ConfirmDeleteMeasuresModal({ measuresId, isOpen, name, onClose }: IConfirmDeleteMeasuresModalProps) {
	const { deleteMeasures } = useDeleteMeasures();

	const handleConfirm = () => {
		deleteMeasures(measuresId);
		onClose();
	};
	return (
		<Modal showCloseButton={false} isOpen={isOpen} onClose={onClose}>
			<div className="flex flex-col items-center space-y-4 p-2 text-center">
				<Trash className="h-12 w-12 text-red-500" />

				<div className="space-y-2">
					<h2 className="text-xl font-semibold">Deletar Medida</h2>
					<p className="text-muted-foreground">
						Você realmente deseja apagar a medida? <span className="text-primary font-medium">{name}</span>
					</p>
				</div>

				<div className="flex gap-3 pt-2">
					<Button variant="outline" onClick={onClose}>
						Cancelar
					</Button>
					<Button className="bg-red-400" onClick={handleConfirm}>
						Confirmar
					</Button>
				</div>
			</div>
		</Modal>
	);
}
