import { removeField<PERSON>tom } from "@/modules/inspection/atoms/forms/fields-form.atom";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { But<PERSON> } from "@/shared/components/shadcn/button";
import { Row } from "@tanstack/react-table";
import { useSet<PERSON>tom } from "jotai";
import { Trash2 } from "lucide-react";

export const InspectionFormActionsRow = ({ row }: { row: Row<ICreateFieldForm> }) => {
	const removeField = useSetAtom(removeFieldAtom);

	const handleRemove = (e: React.MouseEvent) => {
		e.stopPropagation();
		removeField(row.original);
	};

	return (
		<Button
			variant="ghost"
			size="sm"
			onClick={handleRemove}
			className="text-destructive hover:text-destructive flex h-8 w-8 items-center justify-center p-0"
		>
			<Trash2 className="h-4 w-4" />
			<span className="sr-only">Excluir campo</span>
		</Button>
	);
};
