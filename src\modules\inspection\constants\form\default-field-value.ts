import { v4 as uuidv4 } from "uuid";
import { ICreateFieldForm } from "../../validators/form/create-field";

export const createDefaultField = (fields: ICreateFieldForm[]): ICreateFieldForm => ({
	tempId: uuidv4(),
	field: { id: undefined, name: "" },
	groupTitle: "",
	nickname: "",
	required: false,
	group: undefined,
	typeId: undefined,
	measure: { id: undefined, name: "" },
	biFilter: false,
	sequence: fields.length + 1,
});
