import { createUrlQ<PERSON>y<PERSON>tom } from "@/shared/lib/atoms/url-query-atom";
import { TInspectionTabValue } from "../hooks/tabs/inspection-tabs.hook";

export const INSPECTION_TABS_CONFIG = {
	DEFAULT_TAB: "medidas" as const,
	URL_QUERY_KEY: "tab" as const,
} as const;

export const activeTabsAtom = createUrlQueryAtom<TInspectionTabValue>({
	key: INSPECTION_TABS_CONFIG.URL_QUERY_KEY,
	initialValue: INSPECTION_TABS_CONFIG.DEFAULT_TAB,
	parser: (value): TInspectionTabValue => {
		const validTabs: TInspectionTabValue[] = ["medidas", "campos", "vinculo-colaboradores", "formularios", "celulas", "componentes", "vinculos"];
		return validTabs.includes(value as TInspectionTabValue) ? (value as TInspectionTabValue) : INSPECTION_TABS_CONFIG.DEFAULT_TAB;
	},
	serializer: value => value,
});
