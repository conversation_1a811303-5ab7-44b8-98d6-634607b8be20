import { useCreateFormMutation } from "@/modules/inspection/hooks/form/create/create-form-mutation.hook";
import { useCreateForm } from "@/modules/inspection/hooks/form/create/create-form.hook";
import { InspectionFormToCreateMapper } from "@/modules/inspection/lib/mappers/form-to-create.mapper";
import { ICreateForm } from "@/modules/inspection/validators/form/create";
import { Modal } from "@/shared/components/custom/modal";
import { FormCreateForm } from "../form-item/form";

interface IModalCreateFormProps {
	isOpen: boolean;
	onClose: () => void;
}

export const ModalCreateForm: React.FC<IModalCreateFormProps> = ({ isOpen, onClose }) => {
	const { methods, ...formActions } = useCreateForm();
	const { createForm } = useCreateFormMutation();

	const handleSubmit = (data: ICreateForm) => {
		createForm(InspectionFormToCreateMapper.map(data));
		onClose();
	};

	return (
		<Modal
			isOpen={isOpen}
			onClose={onClose}
			className="!h-[95vh] !w-full !max-w-[95vw] md:!h-auto md:!w-[1400px] md:!max-w-none"
			title="Cadastro de Formulário"
		>
			<FormCreateForm mode="create" onClose={onClose} methods={methods} onSubmit={handleSubmit} {...formActions} />
		</Modal>
	);
};
