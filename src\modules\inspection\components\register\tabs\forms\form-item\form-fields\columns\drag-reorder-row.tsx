import { fieldSelect<PERSON><PERSON>, selectedField<PERSON><PERSON><PERSON>tom } from "@/modules/inspection/atoms/forms/fields-form.atom";
import { InspectionFormTypeEnum } from "@/modules/inspection/constants/form/type-enum";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { Button } from "@/shared/components/shadcn/button";
import { TableCell, TableRow } from "@/shared/components/shadcn/table";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { flexRender, Row } from "@tanstack/react-table";
import { useAtomValue, useSetAtom } from "jotai";
import { GripVertical } from "lucide-react";
import { InspectionFieldOptions } from "../options/field-options";

export const DragReorderRow = ({ id }: { id: string }) => {
	const { attributes, listeners } = useSortable({
		id,
	});

	return (
		<Button {...attributes} {...listeners} variant="ghost" size="icon" className="text-muted-foreground size-7 cursor-grab hover:bg-transparent">
			<GripVertical className="text-muted-foreground size-3" />
			<span className="sr-only">Arraste para reordenar</span>
		</Button>
	);
};

export function DraggableRow({ row }: { row: Row<ICreateFieldForm> }) {
	const { transform, transition, setNodeRef, isDragging } = useSortable({ id: row.original.tempId });
	const setSelectedRow = useSetAtom(fieldSelectAtom);
	const selected = useAtomValue(selectedFieldDataAtom);
	const showOptions = selected?.tempId === row.original.tempId && row.original.typeId === InspectionFormTypeEnum.OPTIONS;

	return (
		<>
			<TableRow
				ref={setNodeRef}
				data-state={row.getIsSelected() && "selected"}
				data-dragging={isDragging}
				onClick={() => setSelectedRow(row.original.tempId)}
				className={`relative z-0 ${isDragging ? "z-10 opacity-80" : ""} ${selected?.tempId === row.original.tempId ? "bg-muted" : ""}`}
				style={{ transform: CSS.Transform.toString(transform), transition }}
			>
				{row.getVisibleCells().map(cell => (
					<TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
				))}
			</TableRow>
			{showOptions && (
				<TableRow>
					<TableCell colSpan={row.getVisibleCells().length}>
						<InspectionFieldOptions row={row} />
					</TableCell>
				</TableRow>
			)}
		</>
	);
}
