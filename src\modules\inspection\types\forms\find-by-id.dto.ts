import { InspectionFormTypeEnum } from "../../constants/form/type-enum";

export interface IFormFindByIdDto {
	id: number;
	title: string;
	text: string;
	nomenclature: string;
	revision: string;
	developer: InspectionFormGroup;
	approver: InspectionFormGroup;
	fields: Field[];
}

export interface Field {
	id?: number;
	nickname: string;
	required: boolean;
	fieldType: InspectionFormGroup;
	field: InspectionFormGroup;
	measure: Measure;
	group: number;
	sequence: number;
	groupTitle: string;
	biFilter: boolean;
	options?: Option[];
}

interface Option {
	id: number;
	sequence: number;
	option: string;
}

interface Measure {
	id: number;
	name: string;
	abbreviation: string;
}

interface InspectionFormGroup {
	id: InspectionFormTypeEnum;
	name: string;
}
