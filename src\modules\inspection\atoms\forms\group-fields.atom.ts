import { atom } from "jotai";
import { ICreateFieldForm } from "../../validators/form/create-field";
import { fieldsForm<PERSON>tom } from "./fields-form.atom";

export interface IGroup {
	id: number;
	title: string;
	tempId: string;
}

export const groupsAtom = atom<Array<IGroup>>([]);

export const addGroupAtom = atom(null, (get, set, group: IGroup) => {
	const groups = get(groupsAtom);
	set(groupsAtom, [...groups, group]);
});

export const updateGroupTitleAtom = atom(null, (get, set, { tempId, title }: { tempId: string; title: string }) => {
	const groups = get(groupsAtom);
	const updatedGroups = groups.map(group => (group.tempId === tempId ? { ...group, title } : group));
	set(groupsAtom, updatedGroups);
});

export const removeGroupAtom = atom(null, (get, set, groupTempId: string) => {
	const groups = get(groupsAtom);
	const fields = get(fieldsForm<PERSON>tom);
	const groupToRemove = groups.find(group => group.tempId === groupTempId);
	if (!groupToRemove) return;
	const updatedGroups = groups.filter(group => group.tempId !== groupTempId);
	set(groupsAtom, updatedGroups);
	const updatedFields = fields.map(field => (field.group === groupToRemove.id ? { ...field, group: undefined, groupTitle: "" } : field));
	set(fieldsFormAtom, updatedFields);
});

export const addFieldToGroupAtom = atom(null, (get, set, { groupId, newField }: { groupId: number; newField: ICreateFieldForm }) => {
	const fields = get(fieldsFormAtom);
	const groups = get(groupsAtom);
	const group = groups.find(g => g.id === groupId);
	if (!group) return;
	const fieldsInGroup = fields.filter(field => field.group === groupId);
	const nextSequenceInGroup = fieldsInGroup.length + 1;

	const fieldWithGroup = {
		...newField,
		group: groupId,
		groupTitle: group.title,
		sequence: nextSequenceInGroup,
	};

	set(fieldsFormAtom, [...fields, fieldWithGroup]);
});

export const getFieldsByGroupAtom = atom(get => {
	const fields = get(fieldsFormAtom);
	const groups = get(groupsAtom);

	const groupedFields = new Map<number | undefined, ICreateFieldForm[]>();

	fields.forEach(field => {
		const groupId = field.group;
		if (!groupedFields.has(groupId)) {
			groupedFields.set(groupId, []);
		}
		groupedFields.get(groupId)!.push(field);
	});

	// Sort fields within each group by sequence
	groupedFields.forEach(fieldsInGroup => {
		fieldsInGroup.sort((a, b) => a.sequence - b.sequence);
	});

	return { groupedFields, groups };
});

export const getNextGroupIdAtom = atom(get => {
	const groups = get(groupsAtom);
	return groups.length > 0 ? Math.max(...groups.map(g => g.id)) + 1 : 1;
});

export const updateFieldSequencesInGroupAtom = atom(null, (get, set, groupId: number | undefined) => {
	const fields = get(fieldsFormAtom);
	const fieldsInGroup = fields.filter(field => field.group === groupId);

	// Update sequences to be sequential within the group
	const updatedFields = fields.map(field => {
		if (field.group === groupId) {
			const indexInGroup = fieldsInGroup.findIndex(f => f.tempId === field.tempId);
			return { ...field, sequence: indexInGroup + 1 };
		}
		return field;
	});

	set(fieldsFormAtom, updatedFields);
});
