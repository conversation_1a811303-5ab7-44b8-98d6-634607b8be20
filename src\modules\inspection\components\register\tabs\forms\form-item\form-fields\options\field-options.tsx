import { addFieldOption<PERSON>tom, getFieldOptionsAtom, reorderFieldOptionsAtom } from "@/modules/inspection/atoms/forms/fields-form.atom";
import { InspectionFormTypeEnum } from "@/modules/inspection/constants/form/type-enum";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { Button } from "@/shared/components/shadcn/button";
import { closestCenter, DndContext, DragEndEvent, KeyboardSensor, MouseSensor, TouchSensor, useSensor, useSensors } from "@dnd-kit/core";
import { restrictToParentElement, restrictToVerticalAxis } from "@dnd-kit/modifiers";
import { SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { Row } from "@tanstack/react-table";
import { useAtomValue, useSetAtom } from "jotai";
import { Plus } from "lucide-react";
import { useEffect, useId, useMemo, useRef } from "react";
import { v4 as uuidv4 } from "uuid";
import { DraggableOption } from "./draggable-option";

export const InspectionFieldOptions = ({ row }: { row: Row<ICreateFieldForm> }) => {
	const { tempId, typeId } = row.original;
	const addFieldOption = useSetAtom(addFieldOptionAtom);
	const fieldOptions = useAtomValue(getFieldOptionsAtom);
	const reorderFieldOptions = useSetAtom(reorderFieldOptionsAtom);
	const lastInputRef = useRef<HTMLInputElement>(null);
	const sortableId = useId();

	const sensors = useSensors(useSensor(MouseSensor, {}), useSensor(TouchSensor, {}), useSensor(KeyboardSensor, {}));

	const options = fieldOptions(tempId);
	const optionIds = useMemo(() => options.map(option => option.tempId), [options]);

	const handleAddOption = () => {
		addFieldOption({
			tempId,
			newOption: { option: "", tempId: uuidv4() },
		});
	};

	const handleDragEnd = (event: DragEndEvent) => {
		const { active, over } = event;
		if (active && over && active.id !== over.id) {
			const activeIndex = optionIds.indexOf(String(active.id));
			const overIndex = optionIds.indexOf(String(over.id));
			if (activeIndex !== -1 && overIndex !== -1) {
				reorderFieldOptions({ tempId, activeIndex, overIndex });
			}
		}
	};

	useEffect(() => {
		if (typeId === InspectionFormTypeEnum.OPTIONS && options.length > 0 && lastInputRef.current) {
			lastInputRef.current.focus();
		}
	}, [options.length, typeId]);

	if (typeId !== InspectionFormTypeEnum.OPTIONS) return null;

	return (
		<div className="border-muted space-y-4 border-b p-2">
			<div className="flex items-center justify-between">
				<h4 className="text-sm font-medium">Opções</h4>
				<Button type="button" variant="outline" size="sm" onClick={handleAddOption}>
					<Plus className="mr-1 h-4 w-4" />
					Adicionar Opção
				</Button>
			</div>
			{options.length === 0 ? (
				<p className="text-muted-foreground text-sm">{`Nenhuma opção adicionada. Clique em "Adicionar Opção" para começar.`}</p>
			) : (
				<DndContext
					collisionDetection={closestCenter}
					modifiers={[restrictToVerticalAxis, restrictToParentElement]}
					onDragEnd={handleDragEnd}
					sensors={sensors}
					id={sortableId}
				>
					<SortableContext items={optionIds} strategy={verticalListSortingStrategy}>
						<div className="space-y-2">
							{options.map((option, index) => (
								<DraggableOption
									key={option.tempId}
									option={option}
									fieldTempId={tempId}
									isLast={index === options.length - 1}
									ref={index === options.length - 1 ? lastInputRef : undefined}
								/>
							))}
						</div>
					</SortableContext>
				</DndContext>
			)}
		</div>
	);
};
