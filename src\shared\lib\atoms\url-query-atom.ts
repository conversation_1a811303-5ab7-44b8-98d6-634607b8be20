import { atom } from "jotai";

export interface IUrlQueryAtomConfig<T> {
	key: string;
	initialValue: T;
	parser: (value: string | null) => T;
	serializer: (value: T) => string | null;
}

export function createUrlQueryAtom<T>({ key, initialValue, parser, serializer }: IUrlQueryAtomConfig<T>) {
	const getInitialValueFromUrl = (): T => {
		if (typeof window === "undefined") return initialValue;

		try {
			const searchParams = new URLSearchParams(window.location.search);
			const urlValue = searchParams.get(key);
			return urlValue !== null ? parser(urlValue) : initialValue;
		} catch (error) {
			console.warn(`Erro ao parsear valor da URL para key "${key}":`, error);
			return initialValue;
		}
	};

	const baseAtom = atom<T>(getInitialValueFromUrl());
	const derivedAtom = atom(
		get => get(baseAtom),
		(_get, set, newValue: T) => {
			if (typeof window !== "undefined") {
				try {
					const searchParams = new URLSearchParams(window.location.search);
					const serializedValue = serializer(newValue);

					if (serializedValue === null || serializedValue === undefined) {
						searchParams.delete(key);
					} else {
						searchParams.set(key, serializedValue);
					}
					const searchParamsString = searchParams.toString();
					const newUrl = searchParamsString ? `${window.location.pathname}?${searchParamsString}` : window.location.pathname;

					window.history.replaceState({}, "", newUrl);
				} catch (error) {
					console.warn(`Erro ao atualizar URL para key "${key}":`, error);
				}
			}
			set(baseAtom, newValue);
		},
	);

	return derivedAtom;
}

export const urlQueryAtomUtils = {
	string: (key: string, initialValue: string = "") =>
		createUrlQueryAtom({
			key,
			initialValue,
			parser: value => value || initialValue,
			serializer: value => value || null,
		}),

	number: (key: string, initialValue: number = 0) =>
		createUrlQueryAtom({
			key,
			initialValue,
			parser: value => {
				if (!value) return initialValue;
				const parsed = parseInt(value, 10);
				return isNaN(parsed) ? initialValue : parsed;
			},
			serializer: value => value.toString(),
		}),

	boolean: (key: string, initialValue: boolean = false) =>
		createUrlQueryAtom({
			key,
			initialValue,
			parser: value => {
				if (!value) return initialValue;
				return value === "true";
			},
			serializer: value => (value ? "true" : null),
		}),

	stringArray: (key: string, initialValue: string[] = []) =>
		createUrlQueryAtom({
			key,
			initialValue,
			parser: value => {
				if (!value) return initialValue;
				try {
					return JSON.parse(value);
				} catch {
					return value.split(",").filter(Boolean);
				}
			},
			serializer: value => (value.length > 0 ? JSON.stringify(value) : null),
		}),

	json: <T>(key: string, initialValue: T) =>
		createUrlQueryAtom({
			key,
			initialValue,
			parser: value => {
				if (!value) return initialValue;
				try {
					return JSON.parse(value);
				} catch {
					return initialValue;
				}
			},
			serializer: value => {
				try {
					return JSON.stringify(value);
				} catch {
					return null;
				}
			},
		}),
};
